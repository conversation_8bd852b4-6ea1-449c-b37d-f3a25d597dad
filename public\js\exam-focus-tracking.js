document.addEventListener('DOMContentLoaded', function() {
    // Configuration
    const CONFIG = {
        WARNING_TIMEOUT: 3000,        // Time to show warning (ms)
        MAX_WARNINGS: 3,              // Maximum allowed warnings before auto-submission
        BLUR_THRESHOLD: 1000,         // Min time (ms) to consider a blur as intentional
        MOBILE_THRESHOLD: 768         // Screen width threshold to consider as mobile
    };

    // LocalStorage keys for persistence
    const STORAGE_KEYS = {
        WARNINGS: 'exam_focus_warnings',
        OUT_OF_FOCUS_COUNT: 'exam_focus_out_of_focus_count',
        EXAM_SESSION: 'exam_session_id',
        WARNING_ACTIVE: 'exam_warning_active',
        WARNING_END_TIME: 'exam_warning_end_time'
    };

    // Get current exam session identifier (could be result_id or any unique identifier)
    const getCurrentExamSession = () => {
        const resultId = document.getElementById('result_id')?.value;
        const sessionKey = document.querySelector('input[name="session_key"]')?.value ||
                          document.getElementById('session_key')?.value;
        return resultId || sessionKey || 'default_session';
    };

    // Load data from localStorage
    const loadFromStorage = () => {
        const currentSession = getCurrentExamSession();
        const storedSession = localStorage.getItem(STORAGE_KEYS.EXAM_SESSION);

        // If this is a different exam session, clear old data
        if (storedSession !== currentSession) {
            localStorage.removeItem(STORAGE_KEYS.WARNINGS);
            localStorage.removeItem(STORAGE_KEYS.OUT_OF_FOCUS_COUNT);
            localStorage.removeItem(STORAGE_KEYS.WARNING_ACTIVE);
            localStorage.removeItem(STORAGE_KEYS.WARNING_END_TIME);
            localStorage.setItem(STORAGE_KEYS.EXAM_SESSION, currentSession);
            return { warnings: 0, outOfFocusCount: 0, warningActive: false, warningEndTime: null };
        }

        // Load existing data for current session
        const warnings = parseInt(localStorage.getItem(STORAGE_KEYS.WARNINGS)) || 0;
        const outOfFocusCount = parseInt(localStorage.getItem(STORAGE_KEYS.OUT_OF_FOCUS_COUNT)) || 0;
        const warningActive = localStorage.getItem(STORAGE_KEYS.WARNING_ACTIVE) === 'true';
        const warningEndTime = parseInt(localStorage.getItem(STORAGE_KEYS.WARNING_END_TIME)) || null;

        console.log('Loaded from localStorage:', { warnings, outOfFocusCount, warningActive, warningEndTime, session: currentSession });
        return { warnings, outOfFocusCount, warningActive, warningEndTime };
    };

    // Save data to localStorage
    const saveToStorage = () => {
        localStorage.setItem(STORAGE_KEYS.WARNINGS, focusState.warnings.toString());
        localStorage.setItem(STORAGE_KEYS.OUT_OF_FOCUS_COUNT, focusState.outOfFocusCount.toString());
        localStorage.setItem(STORAGE_KEYS.WARNING_ACTIVE, focusState.isWarningActive.toString());
        if (focusState.warningEndTime) {
            localStorage.setItem(STORAGE_KEYS.WARNING_END_TIME, focusState.warningEndTime.toString());
        }
        console.log('Saved to localStorage:', {
            warnings: focusState.warnings,
            outOfFocusCount: focusState.outOfFocusCount,
            warningActive: focusState.isWarningActive,
            warningEndTime: focusState.warningEndTime
        });
    };

    // Clear localStorage data (called when exam ends)
    const clearStorage = () => {
        localStorage.removeItem(STORAGE_KEYS.WARNINGS);
        localStorage.removeItem(STORAGE_KEYS.OUT_OF_FOCUS_COUNT);
        localStorage.removeItem(STORAGE_KEYS.WARNING_ACTIVE);
        localStorage.removeItem(STORAGE_KEYS.WARNING_END_TIME);
        localStorage.removeItem(STORAGE_KEYS.EXAM_SESSION);
        console.log('Cleared localStorage data');
    };

    // Load initial data from localStorage
    const initialData = loadFromStorage();

    // State variables
    let focusState = {
        outOfFocusCount: initialData.outOfFocusCount,  // Number of times user clicked outside
        warnings: initialData.warnings,               // Number of warnings shown
        isWarningActive: initialData.warningActive,   // Is warning currently shown
        warningEndTime: initialData.warningEndTime,   // When current warning should end
        lastBlurTime: null,                          // Last time window lost focus
        blurDuration: 0,                             // Total time spent out of focus
        isTerminating: false,                        // Flag to prevent multiple termination attempts
        isMobileDevice: false                        // Flag for mobile devices
    };

    // Detect if user is on mobile device
    const detectMobileDevice = () => {
        // Check screen width
        const isMobileByWidth = window.innerWidth < CONFIG.MOBILE_THRESHOLD;

        // Check user agent for mobile browsers
        const isMobileByAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

        // Check for touch capability
        const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

        focusState.isMobileDevice = isMobileByWidth || isMobileByAgent || isTouchDevice;
        console.log('Device detection:', {
            isMobile: focusState.isMobileDevice,
            width: window.innerWidth,
            userAgent: navigator.userAgent,
            hasTouch: isTouchDevice
        });

        return focusState.isMobileDevice;
    };

    // Handle focus loss
    const handleFocusLoss = () => {
        if (focusState.isWarningActive || focusState.isTerminating) return;

        console.log('Focus lost');
        focusState.lastBlurTime = Date.now();

        // On mobile, only count focus loss if not from a touch event
        // This helps prevent false positives when users tap on inputs
        if (!focusState.isMobileDevice || !focusState.lastTouchTime ||
            (Date.now() - focusState.lastTouchTime > 1000)) {
            focusState.outOfFocusCount++;
            saveToStorage(); // Save to localStorage when count changes
        }
    };

    // Handle focus return
    const handleFocusReturn = () => {
        if (focusState.isTerminating) return;

        console.log('Focus returned');
        if (focusState.lastBlurTime) {
            const blurDuration = Date.now() - focusState.lastBlurTime;
            focusState.blurDuration += blurDuration;

            // Only show warning if blur was intentional (longer than threshold)
            // For mobile devices, use a higher threshold to account for app switching
            const threshold = focusState.isMobileDevice ? CONFIG.BLUR_THRESHOLD * 3 : CONFIG.BLUR_THRESHOLD;

            if (blurDuration > threshold && !focusState.isWarningActive) {
                // On mobile, don't show warning for short focus loss
                // This helps with keyboard appearing/disappearing and other mobile behaviors
                if (!focusState.isMobileDevice || blurDuration > 5000) {
                    console.log('Showing warning after blur duration:', blurDuration);
                    showWarning();
                }
            }

            focusState.lastBlurTime = null;
        }
    };

    // Handle visibility change
    const handleVisibilityChange = () => {
        if (focusState.isTerminating) return;

        if (document.visibilityState === 'hidden') {
            console.log('Page hidden');
            // Same logic as window blur
            if (!focusState.lastBlurTime && !focusState.isWarningActive) {
                focusState.lastBlurTime = Date.now();

                // On mobile, be more lenient with visibility changes
                // as they happen frequently when switching apps or getting notifications
                if (!focusState.isMobileDevice) {
                    focusState.outOfFocusCount++;
                    saveToStorage(); // Save to localStorage when count changes
                }
            }
        } else {
            console.log('Page visible');
            // Same logic as window focus
            if (focusState.lastBlurTime) {
                const blurDuration = Date.now() - focusState.lastBlurTime;
                focusState.blurDuration += blurDuration;

                // For mobile devices, use a higher threshold
                const threshold = focusState.isMobileDevice ? CONFIG.BLUR_THRESHOLD * 3 : CONFIG.BLUR_THRESHOLD;

                if (blurDuration > threshold && !focusState.isWarningActive) {
                    // On mobile, only show warning for longer periods of inactivity
                    if (!focusState.isMobileDevice || blurDuration > 10000) {
                        console.log('Showing warning after visibility change, duration:', blurDuration);
                        showWarning();
                    } else {
                        // For mobile, just count it without showing warning for shorter durations
                        focusState.outOfFocusCount++;
                        saveToStorage(); // Save to localStorage when count changes
                    }
                }

                focusState.lastBlurTime = null;
            }
        }
    };

    // Track touch events to help distinguish between legitimate mobile interactions
    // and actual focus loss events
    const handleTouchStart = () => {
        focusState.lastTouchTime = Date.now();
    };

    // Create warning modal
    const createWarningModal = () => {
        const modal = document.createElement('div');
        modal.id = 'focus-warning-modal';

        // Adjust modal content based on device type
        const modalContent = focusState.isMobileDevice ?
            `<div class="focus-warning-content" style="background-color: #fff; padding: 20px; border-radius: 10px; text-align: center; max-width: 90%; width: 300px;">
                <div class="focus-warning-header">
                    <h2 style="color: #d9534f; margin-bottom: 15px; font-size: 18px;">CẢNH BÁO</h2>
                </div>
                <div class="focus-warning-body">
                    <p style="font-size: 14px; margin-bottom: 8px;">Bạn đã rời khỏi màn hình bài thi.</p>
                    <p style="font-size: 14px; margin-bottom: 8px;">Việc này có thể được xem là hành vi gian lận.</p>
                    <p style="font-size: 14px; margin-bottom: 8px;">Sau <span id="warning-countdown" style="font-weight: bold;">5</span> giây, bạn có thể tiếp tục làm bài.</p>
                    <p class="focus-warning-note" style="font-size: 14px; margin-bottom: 8px;">Lưu ý: Sau ${CONFIG.MAX_WARNINGS} lần cảnh báo, bài thi sẽ tự động kết thúc!</p>
                </div>
                <div class="focus-warning-footer">
                    <p style="font-weight: bold; margin-top: 15px; font-size: 14px;">Cảnh báo: ${focusState.warnings}/${CONFIG.MAX_WARNINGS}</p>
                </div>
            </div>` :
            `<div class="focus-warning-content" style="background-color: #fff; padding: 30px; border-radius: 10px; text-align: center; max-width: 500px;">
                <div class="focus-warning-header">
                    <h2 style="color: #d9534f; margin-bottom: 20px;">CẢNH BÁO</h2>
                </div>
                <div class="focus-warning-body">
                    <p style="font-size: 16px; margin-bottom: 10px;">Bạn đã rời khỏi màn hình bài thi.</p>
                    <p style="font-size: 16px; margin-bottom: 10px;">Việc này có thể được xem là hành vi gian lận.</p>
                    <p style="font-size: 16px; margin-bottom: 10px;">Sau <span id="warning-countdown" style="font-weight: bold;">5</span> giây, bạn có thể tiếp tục làm bài.</p>
                    <p class="focus-warning-note" style="font-size: 16px; margin-bottom: 10px;">Lưu ý: Sau ${CONFIG.MAX_WARNINGS} lần cảnh báo, bài thi sẽ tự động kết thúc!</p>
                </div>
                <div class="focus-warning-footer">
                    <p style="font-weight: bold; margin-top: 20px;">Cảnh báo: ${focusState.warnings}/${CONFIG.MAX_WARNINGS}</p>
                </div>
            </div>`;

        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            z-index: 10000;
            display: flex;
            justify-content: center;
            align-items: center;
        `;

        modal.innerHTML = modalContent;
        document.body.appendChild(modal);

        return modal;
    };

    // Show warning
    const showWarning = () => {
        if (focusState.isWarningActive || focusState.isTerminating) return;

        console.log('Showing warning, count:', focusState.warnings + 1);
        focusState.warnings++;
        focusState.isWarningActive = true;
        focusState.warningEndTime = Date.now() + CONFIG.WARNING_TIMEOUT; // Set when warning should end
        saveToStorage(); // Save to localStorage when warning count changes

        const modal = document.getElementById('focus-warning-modal') || createWarningModal();
        const countdownEl = document.getElementById('warning-countdown');

        // Update warning count
        const footerText = modal.querySelector('.focus-warning-footer p');
        if (footerText) {
            footerText.textContent = `Cảnh báo: ${focusState.warnings}/${CONFIG.MAX_WARNINGS}`;
        }

        // Kiểm tra nếu đây là cảnh báo cuối cùng
        if (focusState.warnings >= CONFIG.MAX_WARNINGS) {
            const warningHeader = modal.querySelector('.focus-warning-header h2');
            if (warningHeader) {
                warningHeader.textContent = 'CẢNH BÁO CUỐI CÙNG';
                warningHeader.style.color = '#ff0000';
            }

            const warningNote = modal.querySelector('.focus-warning-note');
            if (warningNote) {
                warningNote.innerHTML = '<strong style="color: #ff0000;">Đây là cảnh báo cuối cùng. Bài thi sẽ kết thúc sau khi đóng thông báo này!</strong>';
            }
        }

        modal.style.display = 'flex';

        // Disable all inputs during warning
        toggleInputs(false);

        // Start countdown
        let countdown = CONFIG.WARNING_TIMEOUT / 1000;
        if (countdownEl) {
            countdownEl.textContent = countdown;
        }

        const timer = setInterval(() => {
            countdown--;
            if (countdownEl) {
                countdownEl.textContent = countdown;
            }

            if (countdown <= 0) {
                clearInterval(timer);
                modal.style.display = 'none';
                focusState.isWarningActive = false;
                focusState.warningEndTime = null;
                saveToStorage(); // Update localStorage to clear warning state
                toggleInputs(true);

                // If max warnings reached, end the test immediately
                if (focusState.warnings >= CONFIG.MAX_WARNINGS) {
                    console.log('Max warnings reached, ending exam immediately');
                    endExam();
                }
            }
        }, 1000);
    };

    // Toggle input elements
    const toggleInputs = (enable) => {
        const inputs = document.querySelectorAll('input, textarea, select, button');
        inputs.forEach(input => {
            input.disabled = !enable;
        });
    };

    // End exam function - this will handle automatic submission
    const endExam = () => {
        // Prevent multiple termination attempts
        if (focusState.isTerminating) return;

        console.log('Ending exam due to focus violations');
        focusState.isTerminating = true;

        // Clear localStorage data when exam ends
        clearStorage();

        // Disable all inputs immediately
        const inputs = document.querySelectorAll('input, textarea, select, button');
        inputs.forEach(input => {
            input.disabled = true;
        });

        // Get necessary information for redirect
        const resultId = document.getElementById('result_id')?.value;
        const sessionKey = document.querySelector('input[name="session_key"]')?.value ||
                          document.getElementById('session_key')?.value;

        console.log('Redirecting to end page immediately with:', { resultId, sessionKey });

        // Redirect immediately without countdown
        if (typeof urlEndTest !== 'undefined') {
            console.log('Using urlEndTest:', urlEndTest);
            window.location.href = urlEndTest;
            return;
        }

        // If we have result_id and sessionKey, construct the URL
        if (resultId && sessionKey) {
            const endUrl = `/exams-end/${resultId}/${sessionKey}`;
            console.log('Constructed URL:', endUrl);
            window.location.href = endUrl;
            return;
        }

        // Fallback: redirect to home
        console.log('No redirect info found, going to home');
        window.location.href = '/';
    };

    // Direct exam submission function
    const submitExamDirectly = () => {
        // Find the form that would be used for submission
        const resultId = document.getElementById('result_id').value;

        // Create a form data object for submission
        const formData = new FormData();
        formData.append('result_id', resultId);
        formData.append('auto_terminated', true);
        formData.append('warnings_count', focusState.warnings);
        formData.append('out_of_focus_count', focusState.outOfFocusCount);

        // Collect any essay answers
        document.querySelectorAll('.essay-textarea').forEach(function(textarea) {
            const questionId = textarea.dataset.q;
            formData.append(`literature_answer${questionId}`, textarea.value.trim());
        });

        // Add extra question answer if present
        const numPerson = document.getElementById('num_person');
        if (numPerson) {
            formData.append('num_person', numPerson.value || '0');
        }

        // Submit directly to the server
        $.ajax({
            url: urlPostGuess, // This should be the URL for submitting the exam
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                // Redirect to end test page
                window.location.href = urlEndTest;
            },
            error: function() {
                // Fallback if AJAX fails - try direct click
                const endQuizBtn = document.querySelector('.end-quiz');
                if (endQuizBtn && typeof endQuizBtn.click === 'function') {
                    // Programmatically bypass confirmation dialog
                    // First monkey patch bootbox to skip confirmation
                    if (window.bootbox && window.bootbox.confirm) {
                        const originalConfirm = window.bootbox.confirm;
                        window.bootbox.confirm = function(options, callback) {
                            if (typeof options === 'object' && typeof callback === 'function') {
                                // Skip dialog and call callback with true
                                callback(true);
                            } else if (typeof options === 'object' && options.callback) {
                                // Skip dialog and call options.callback with true
                                options.callback(true);
                            } else if (typeof options === 'string' && typeof callback === 'function') {
                                // Skip dialog and call callback with true
                                callback(true);
                            }
                        };
                    }

                    // Now try clicking the button
                    endQuizBtn.click();

                    // Extra fallback - direct navigation
                    setTimeout(function() {
                        window.location.href = urlEndTest;
                    }, 2000);
                } else {
                    // Last resort - direct navigation
                    window.location.href = urlEndTest;
                }
            }
        });
    };

    // Override the bootbox confirm for the end quiz button
    const overrideEndQuizConfirmation = () => {
        // Check if bootbox is loaded
        if (window.bootbox && window.bootbox.confirm) {
            // Store the original confirm function
            const originalConfirm = window.bootbox.confirm;

            // Override bootbox.confirm for auto-terminated exams
            window.bootbox.confirm = function(options, callback) {
                // If this is an auto-terminated exam, skip confirmation
                if (focusState.isTerminating) {
                    if (typeof options === 'object' && typeof callback === 'function') {
                        callback(true); // Simulate "yes" response
                        return { modal: { remove: function() {} } }; // Return dummy modal
                    } else if (typeof options === 'object' && options.callback) {
                        options.callback(true); // Simulate "yes" response
                        return { modal: { remove: function() {} } }; // Return dummy modal
                    } else if (typeof options === 'string' && typeof callback === 'function') {
                        callback(true); // Simulate "yes" response
                        return { modal: { remove: function() {} } }; // Return dummy modal
                    }
                }

                // Otherwise, call the original function
                return originalConfirm.apply(this, arguments);
            };
        }
    };

    // Add this function before the init() call
    function updateTrackingData(isEnding = false) {
        // Collect focus tracking data
        const trackingData = {
            totalTime: Date.now() - focusState.startTime,
            focusTime: focusState.focusTime,
            blurTime: focusState.blurTime,
            blurCount: focusState.blurCount,
            isEnding: isEnding
        };

        // Send data to server
        fetch(CONFIG.TRACK_ENDPOINT, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
            },
            body: JSON.stringify(trackingData)
        }).catch(error => {
            console.error('Error sending tracking data:', error);
        });

        console.log('Focus tracking data updated', trackingData);
    }

    // Check and restore warning state after page reload
    const checkWarningState = () => {
        if (focusState.isWarningActive && focusState.warningEndTime) {
            const now = Date.now();
            const remainingTime = focusState.warningEndTime - now;

            if (remainingTime > 0) {
                // Warning is still active, continue the countdown
                console.log('Restoring active warning with', Math.ceil(remainingTime / 1000), 'seconds remaining');

                // If this is the final warning (warnings >= MAX_WARNINGS), end exam immediately
                if (focusState.warnings >= CONFIG.MAX_WARNINGS) {
                    console.log('Final warning was active during reload - ending exam immediately');
                    endExam();
                    return;
                }

                // Otherwise, show the warning modal and continue countdown
                const modal = createWarningModal();
                modal.style.display = 'flex';
                toggleInputs(false);

                const countdownEl = document.getElementById('warning-countdown');
                let countdown = Math.ceil(remainingTime / 1000);

                if (countdownEl) {
                    countdownEl.textContent = countdown;
                }

                const timer = setInterval(() => {
                    countdown--;
                    if (countdownEl) {
                        countdownEl.textContent = countdown;
                    }

                    if (countdown <= 0) {
                        clearInterval(timer);
                        modal.style.display = 'none';
                        focusState.isWarningActive = false;
                        focusState.warningEndTime = null;
                        saveToStorage();
                        toggleInputs(true);

                        // Check if max warnings reached after this warning ends
                        if (focusState.warnings >= CONFIG.MAX_WARNINGS) {
                            console.log('Max warnings reached after restored warning, ending exam immediately');
                            endExam();
                        }
                    }
                }, 1000);
            } else {
                // Warning time has expired
                focusState.isWarningActive = false;
                focusState.warningEndTime = null;
                saveToStorage();

                // If this was the final warning, end the exam immediately
                if (focusState.warnings >= CONFIG.MAX_WARNINGS) {
                    console.log('Final warning expired during reload - ending exam immediately');
                    endExam();
                }
            }
        }
    };

    // Initialize
    const init = () => {
        console.log('Initializing focus tracking');

        // Log initial state from localStorage
        if (focusState.warnings > 0 || focusState.outOfFocusCount > 0) {
            console.log('Restored from localStorage - Warnings:', focusState.warnings, 'Out of focus count:', focusState.outOfFocusCount);
        }

        // Check if there was an active warning before page reload
        checkWarningState();

        // Detect device type
        detectMobileDevice();

        // Set up event listeners for focus/blur events
        window.addEventListener('blur', handleFocusLoss);
        window.addEventListener('focus', handleFocusReturn);
        document.addEventListener('visibilitychange', handleVisibilityChange);

        // Add touch event listeners for mobile devices
        if (focusState.isMobileDevice) {
            document.addEventListener('touchstart', handleTouchStart);

            // Listen for orientation changes which can cause focus issues on mobile
            window.addEventListener('orientationchange', () => {
                console.log('Orientation changed');
                // Give the browser time to adjust
                setTimeout(() => {
                    // Re-detect device properties after orientation change
                    detectMobileDevice();
                }, 500);
            });

            console.log('Mobile-specific event handlers added');
        }

        overrideEndQuizConfirmation();

        // Save tracking data when exam ends
        document.querySelector('.end-quiz')?.addEventListener('click', function() {
            updateTrackingData(true);
            // Clear localStorage when exam ends normally
            clearStorage();
        });

        // Periodically update tracking data
        setInterval(function() {
            updateTrackingData();
        }, CONFIG.TRACK_INTERVAL);

        console.log('Focus tracking initialized for', focusState.isMobileDevice ? 'mobile' : 'desktop');
    };

    // Start tracking
    init();
});
