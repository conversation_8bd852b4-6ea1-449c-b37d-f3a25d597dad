<?php

/*
|--------------------------------------------------------------------------|
| Test Routes - Phần dùng để test, sẽ xóa khi deploy
|--------------------------------------------------------------------------|
*/
Route::prefix('test')->group(function () {
    Route::get('/', 'TestController@index')->name('test');
    Route::post('/one', 'TestController@one')->name('test-one');
    Route::post('/multi', 'TestController@multi')->name('test-multi');
    Route::get('/download', 'TestController@download')->name('test-download');
    Route::get('/test', 'TestController@test');
    Route::get('/test-payment/{name}/{amount}', 'TestController@testPayment');
    Route::get("/exportExcel/{type}", "TestController@export");
});

Route::get('welcome', function(){return view('welcome');});

/*
  |--------------------------------------------------------------------------
  | Public Web Routes - Các request không yêu cầu phải đăng nhập được viết ở đây
  |--------------------------------------------------------------------------
  | Các request cho tất cả người truy cập
  |
 */
//Trang chủ web
Route::get('/', 'HomeController@web')->name('home');
Route::get('/home', 'HomeController@web');
// kết quả
Route::get('ket-qua', 'Web\ExamsController@ketqua');
Route::get('chitiet-ketqua/{exam_id}', 'Web\ExamsController@chitietKetqua')->name('chitiet-ketqua');

//Tin tức
//Route::resource('danh-muc-tin-tuc', 'Web\NewsGroupsWebController');
Route::get('danh-muc-tin-tuc/{path}', 'Web\NewsGroupsWebController@show');
Route::resource('tin-tuc', 'Web\NewsWebController');
Route::resource('news-comments', 'Web\NewsCommentsWebController');

//Văn bản
Route::get('van-ban-qppl/data', 'Web\DocumentController@indexData')->name('van-ban-qppl.indexData');
Route::get('van-ban-qppl', 'Web\DocumentController@index')->name('van-ban-qppl.index');
Route::get('van-ban-qppl/{path}', 'Web\DocumentController@show')->name('van-ban-qppl.show');

//Thư viện video
Route::get('video', 'Web\VideoWebController@index');
Route::get('/video/{path}', 'Web\VideoWebController@showVideo');

//Thu viện ảnh
Route::get('/thu-vien-anh/', 'Web\ImagesWebController@index');
Route::get('/thu-vien-anh/{path}', 'Web\ImagesWebController@showImage');

//Liên hệ, phản hồi thông tin
Route::resource('lien-he', 'Web\ContactController');

//Trợ giúp, hỏi đáp
Route::get('/tro-giup', 'Web\HelpWebController@index')->name('tro-giup');
Route::post('add-question', 'Web\HelpWebController@addHelps');
Route::get('/tro-giup/{path}', 'Web\HelpWebController@show');

//Bài viết thông tin fix
Route::get('/about/{path}', 'Web\NewsWebController@about');

Route::get('/thong-ke/{path}', 'Web\NewsWebController@statistic');
Route::get('/thong-ke/{path}/view', 'Web\NewsWebController@statisticView');

//QRcode
Route::get('get-app-qrcode', 'Web\QRCodeController@index');
Route::get('get-app', 'Web\QRCodeController@show');

// CAPTCHA verification
Route::post('/exams-verify-captcha', 'Web\ExamsController@verifyCaptcha')->name('exams-verify-captcha');

// Session data logging
Route::post('/exams-log-session', 'Web\ExamsController@logSessionData')->name('exams-log-session');

// Survey routes
Route::get('khao-sat', 'Web\SurveyController@index')->name('surveys.index');
Route::get('khao-sat/{id}', 'Web\SurveyController@show')->name('surveys.show')->where('id', '[0-9]+');
Route::get('khao-sat/{slug}', 'Web\SurveyController@showBySlug')->name('surveys.showBySlug')->where('slug', '[a-z0-9\-]+');
Route::post('khao-sat/{id}/submit', 'Web\SurveyController@submit')->name('surveys.submit')->where('id', '[0-9]+');
Route::get('khao-sat/{id}/cam-on', 'Web\SurveyController@thankyou')->name('surveys.thankyou')->where('id', '[0-9]+');
Route::get('khao-sat/{id}/ket-qua', 'Web\SurveyController@results')->name('surveys.results')->where('id', '[0-9]+');

/*
  |--------------------------------------------------------------------------
  | Auth Web Routes - Các request yêu cầu phải đăng nhập được viết ở đây
  |--------------------------------------------------------------------------
  | VD: các request cho thành viên, vd: cập nhật thông tin cá nhân, đặt hàng...
  |
 */
Route::middleware(['auth'])->group(function () {
    //Phần thi viết ở đây
    Route::get('exams', 'Web\ExamsController@index');
    Route::get('exams-testing', 'Web\ExamsController@examsTesting');
    Route::post('exams-insert-answer', 'Web\ExamsController@insertAnswer');
    Route::get('exams-end/{result_id}/{session_key}', 'Web\ExamsController@examsTestingEnd')->name('exams-end');
    Route::post('exams-finish-testing', 'Web\ExamsController@finishTesting');
    Route::get('show-captcha', 'Web\CaptchaController@showCaptcha');
    Route::get('reload-captcha', 'Web\CaptchaController@reload');
    Route::post('check-captcha', 'Web\CaptchaController@check');
    Route::get('/get-fill-blank-answers', 'Web\ExamsController@getFillBlankAnswers')->name('get.fill.blank.answers');

    //================= PROFLE USER ==========
    Route::prefix('user')->group(function () {
        //Trang chính của user (profile hoặc tin nhắn): Message: Web\UserMessageController@index
        Route::get('/', 'Web\ProfileController@index')->name('profile');
        //kết quả thi
        Route::get('ket-qua', 'Web\ExamsController@quizResults')->name('user.results');
        Route::get('quiz-result-detail/{result_id}', 'Web\ExamsController@quizResultDetail')->name('user.quiz-result-detail');

        //Sửa thông cá nhân
        Route::get('/profile', 'Web\ProfileController@index')->name('user.profile');
        Route::get('/profile/{user_id}', 'Web\ProfileController@index');
        Route::post('profile/change-avatar', 'Web\ProfileController@changeAvatar');
        Route::post('profile/remove-avatar', 'Web\ProfileController@removeAvatar');
        Route::post('profile/update', 'Web\ProfileController@update');
        Route::post('profile/change-password', 'Web\ProfileController@changePassword');
        Route::get('profile/district/{id}', 'Web\ProfileController@getDistrict');
        Route::get('profile/commune/{id}', 'Web\ProfileController@getCommune');

        //Cập nhật email, sđt
        Route::get('account/edit/{type}', 'Web\ProfileController@showEditUsernameForm')->name('user.account-edit');
        Route::post('account/send', 'Web\ProfileController@sendVerifyCode')->name('user.account-send');
        Route::get('account/verify/{type}/{username}', 'Web\ProfileController@showVerifyUsernameForm')->name('user.account-verify');
        Route::get('account/resend/{username}', 'Web\ProfileController@resendVerifyCode')->name('user.account-resend');
        Route::post('account/update', 'Web\ProfileController@updateUsername')->name('user.account-update');

        //Thông báo
        Route::get('notifications/data', 'Web\NotificationsController@indexData')->name('notifications.indexData');
        Route::get('notifications/showNotification/{id}', 'Web\NotificationsController@showNotification')->name('notifications.showNotification');
        Route::post('notifications/deleteNotification', 'Web\NotificationsController@deleteNotification')->name('notifications.deleteNotification');
        Route::resource('notifications', 'Web\NotificationsController');
        Route::resource('sessions', 'Web\SessionsController');
        Route::post('logout-other-devices', 'Web\SessionsController@logoutOtherDevices');
    });
    Route::get('info', 'HomeController@showInfo');
});

Route::get('get-regions/{parent_id}', 'HomeController@getRegions')->name('home.getRegions');