(() => {
    "use strict";

    // Add global error handler for the security engine
    window.addEventListener('error', function(event) {
        // Only log errors from our security script
        if (event.filename && event.filename.includes('exam-protection')) {
            console.error('Security Engine Error:', event.message, 'at line', event.lineno);

            // Try to log the error if the security engine is initialized
            if (window._securityEngine && typeof window._securityEngine.sendSecurityLog === 'function') {
                try {
                    window._securityEngine.sendSecurityLog({
                        type: 'security_engine_error',
                        message: event.message,
                        line: event.lineno,
                        filename: event.filename
                    });
                } catch (e) {
                    // Silent catch - don't cause additional errors
                }
            }
        }
    });

    // Config bảo mật
    const SECURITY_CONFIG = {
        SELF_HEAL_INTERVAL: 5000,
        DEBUG_DETECTION_DELAY: 1000,
        ALLOWED_DOMAINS: ["etest.local", "localhost", "localhost:8000"],
        LOG_INTERVAL: 60000,         // Tăng từ 30000 lên 60000 (1 phút)
        LOG_BATCH_SIZE: 20,          // Số lượng log tối đa trong một batch
        LOG_QUEUE_MAX: 100,          // Kích thước tối đa của hàng đợi log
        MOUSE_SAMPLE_RATE: 200,      // Giảm tần suất lấy mẫu (1/200)
        KEYBOARD_TIMING_MAX: 100,    // Lưu tối đa 100 mẫu thời gian gõ phím
        WARNING_TIMEOUT: 5000,       // Thời gian hiển thị cảnh báo (ms)
        MAX_WARNINGS: 3,             // Số cảnh báo tối đa trước khi kết thúc bài thi
        LOG_IMPORTANCE_LEVELS: {     // Mức độ quan trọng của các loại log
            HIGH: 5,                 // Log quan trọng cao (gửi ngay)
            MEDIUM: 3,               // Log quan trọng trung bình (gửi trong batch tiếp theo)
            LOW: 1                   // Log thông thường (có thể bỏ qua nếu quá nhiều)
        }
    };

    class SecurityEngine {
        constructor() {
            try {
                // Khởi tạo các biến theo dõi hành vi
                this.mouseMovements = [];
                this.mouseClicks = [];
                this.mouseAnalysis = {};
                this.keyPressTimings = [];
                this.keyPressSequence = [];
                this.focusEvents = [];
                this.pageLoaded = Date.now();
                this.preventedActions = {
                    copy: 0,
                    paste: 0,
                    rightClick: 0,
                    keyboardShortcuts: 0,
                    devTools: 0
                };

                // Safely generate fingerprint
                try {
                    this.deviceFingerprint = this.generateDeviceFingerprint();
                } catch (e) {
                    this.deviceFingerprint = "error-generating-fingerprint";
                    console.warn("Error generating fingerprint:", e);
                }

                // Use shared localStorage keys with focus-tracking
                this.STORAGE_KEYS = {
                    WARNINGS: 'exam_focus_warnings',
                    OUT_OF_FOCUS_COUNT: 'exam_focus_out_of_focus_count',
                    EXAM_SESSION: 'exam_session_id',
                    WARNING_ACTIVE: 'exam_warning_active',
                    WARNING_END_TIME: 'exam_warning_end_time'
                };

                // Load existing warning state from localStorage
                const existingWarnings = parseInt(localStorage.getItem(this.STORAGE_KEYS.WARNINGS)) || 0;
                const isWarningActive = localStorage.getItem(this.STORAGE_KEYS.WARNING_ACTIVE) === 'true';

                this.warningState = {
                    warnings: existingWarnings,
                    isWarningActive: isWarningActive,
                    isTerminating: false,
                    lastWarningTime: 0
                };

                // Khởi tạo hàng đợi log và biến theo dõi trạng thái gửi log
                this.logQueue = [];
                this.logSendInProgress = false;
                this.lastLogSendTime = 0;
                this.lastMouseAnalysisTime = 0;
                this.lastKeyboardAnalysisTime = 0;

                // Lưu trữ các hàm gốc
                this.originalFunctions = {
                    fetch: window.fetch,
                    eval: window.eval,
                    Function: window.Function,
                    setTimeout: window.setTimeout,
                    setInterval: window.setInterval,
                    requestAnimationFrame: window.requestAnimationFrame
                };

                // Safely check for jQuery
                if (typeof window.jQuery !== 'undefined' && window.jQuery.ajax) {
                    this.originalFunctions.ajax = window.jQuery.ajax;
                } else {
                    this.originalFunctions.ajax = null;
                }

                // Kiểm tra thiết bị di động
                this.isMobileDevice = this.detectMobileDevice();

                // Kiểm tra trạng thái cảnh báo từ localStorage
                this.checkInitialWarningState();

                // Khởi tạo bảo mật
                this.init();
                this.bindSelfHealing();
                this.monitorEnvironment();
                this.initDomProtections();
                this.setupLogReporting();

                // Only call monitorAutoClickTools if it exists
                if (typeof this.monitorAutoClickTools === 'function') {
                    this.monitorAutoClickTools();
                }

                // Lưu instance vào biến toàn cục để có thể truy cập từ bên ngoài
                window._securityEngine = this;

                // Thêm listener cho debugging events
                window.addEventListener('devtoolschange', e => {
                    if (e && e.detail && e.detail.open) {
                        console.log("DevTools opened");
                        this.addToLogQueue({
                            type: 'devtools_detected',
                            importance: SECURITY_CONFIG.LOG_IMPORTANCE_LEVELS.HIGH,
                            details: 'DevTools opened via devtoolschange event'
                        });
                        // window.location.reload();
                    }
                });

                // Theo dõi sự kiện beforeunload để gửi log cuối cùng
                window.addEventListener('beforeunload', () => {
                    if (typeof this.sendFinalLogs === 'function') {
                        this.sendFinalLogs();
                    }
                });

                console.log("Security Engine initialized successfully");
            } catch (error) {
                console.error("Security Engine initialization failed:", error);
            }
        }

        // Phát hiện thiết bị di động
        detectMobileDevice() {
            // Kiểm tra chiều rộng màn hình
            const isMobileByWidth = window.innerWidth < SECURITY_CONFIG.MOBILE_THRESHOLD;

            // Kiểm tra user agent
            const isMobileByAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            // Kiểm tra khả năng cảm ứng
            const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

            return isMobileByWidth || isMobileByAgent || isTouchDevice;
        }

        // Khởi tạo các tính năng bảo mật
        init() {
            try {
                this.verifyDomain();
                this.disableDevTools();
                this.injectSecurityHooks();
                this.initFrameProtection();
                this.overrideTimingFunctions();
                console.log("Security Engine Initialized");
            } catch (error) {
                console.error("Security initialization failed:", error);
            }
        }

        // Kiểm tra domain
        verifyDomain() {
            const currentDomain = window.location.hostname + (window.location.port ? ':' + window.location.port : '');
            if (!SECURITY_CONFIG.ALLOWED_DOMAINS.includes(currentDomain)) {
                console.warn(`Unauthorized domain: ${currentDomain}`);
            }
        }

        // Tạo fingerprint thiết bị
        generateDeviceFingerprint() {
            const components = [
                navigator.userAgent,
                navigator.language,
                screen.colorDepth,
                screen.width + 'x' + screen.height,
                new Date().getTimezoneOffset(),
                !!window.sessionStorage,
                !!window.localStorage,
                !!window.indexedDB
            ];

            let fingerprint = components.join('###');
            let hash = 0;

            for (let i = 0; i < fingerprint.length; i++) {
                hash = ((hash << 5) - hash) + fingerprint.charCodeAt(i);
                hash |= 0; // Convert to 32bit integer
            }

            return hash.toString(16);
        }

        // Vô hiệu hóa DevTools
        disableDevTools() {
            // Chặn F12 - chỉ áp dụng cho desktop
            if (!this.isMobileDevice) {
                document.addEventListener('keydown', (e) => {
                    if (e.key === 'F12' ||
                        // Chặn Ctrl+Shift+I / Cmd+Option+I
                        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
                        (e.metaKey && e.altKey && e.key === 'i') ||
                        // Chặn Ctrl+Shift+J / Cmd+Option+J
                        (e.ctrlKey && e.shiftKey && e.key === 'J') ||
                        (e.metaKey && e.altKey && e.key === 'j') ||
                        // Chặn Ctrl+Shift+C / Cmd+Option+C
                        (e.ctrlKey && e.shiftKey && e.key === 'C') ||
                        (e.metaKey && e.altKey && e.key === 'c') ||
                        // Chặn Ctrl+U / Cmd+U (view source)
                        (e.ctrlKey && e.key === 'u') ||
                        (e.metaKey && e.key === 'u')
                    ) {
                        e.preventDefault();
                        this.preventedActions.keyboardShortcuts++;
                        return false;
                    }
                }, true);

                // Phát hiện khi DevTools mở bằng cách kiểm tra kích thước cửa sổ
                let devtools = function() {};
                devtools.toString = function() {
                    this.opened = true;
                }

                // Kiểm tra liên tục - chỉ cho desktop
                setInterval(() => {
                    const widthThreshold = window.outerWidth - window.innerWidth > 160;
                    const heightThreshold = window.outerHeight - window.innerHeight > 160;

                    if (widthThreshold || heightThreshold) {
                        // DevTools có thể đã được mở
                        console.log("DevTools đã bị phát hiện. Vui lòng tải lại trang.");
                        this.preventedActions.devTools++;
                        this.sendSecurityLog({type: 'devtools_detected', method: 'size_difference'});
                        // window.location.reload();
                    }

                    // Phát hiện thông qua console
                    console.log(devtools);
                }, 1000);
            }

            // Vô hiệu hóa right-click - áp dụng cho cả mobile và desktop
            window.addEventListener('contextmenu', (e) => {
                if (e && e.target && (
                    e.target.tagName === 'INPUT' ||
                    e.target.tagName === 'TEXTAREA' ||
                    (e.target.classList && e.target.classList.contains('fill-blank-input')) ||
                    (e.target.classList && e.target.classList.contains('essay-textarea'))
                )) {
                    return true;
                }

                e.preventDefault();
                this.preventedActions.rightClick++;
                return false;
            }, true);

            // Lưu console gốc để sử dụng trong báo cáo lỗi
            this._originalConsole = {
                log: console.log.bind(console),
                info: console.info.bind(console),
                warn: console.warn.bind(console),
                debug: console.debug.bind(console),
                error: console.error.bind(console)
            };

            // Vô hiệu hóa một số object debug
            window.console = {
                log: function() {},
                info: function() {},
                warn: function() {},
                debug: function() {},
                error: function() {}
            };
        }

        // Tiêm security hooks
        injectSecurityHooks() {
            window.fetch = async (...args) => {
                this.detectModification();
                // Cho phép gọi API log của chúng ta
                if (args[0] === '/exams-log-session') {
                    return this.originalFunctions.fetch(...args);
                }
                return this.originalFunctions.fetch(...args);
            };

            window.eval = (...args) => {
                console.warn("Eval detected");
                this.sendSecurityLog({type: 'eval_attempt', content: String(args[0]).substring(0, 100)});
                this.showWarning("Phát hiện hành vi đáng ngờ: Sử dụng eval()");
                return null;
            };

            window.Function = (...args) => {
                console.warn("Function constructor detected");
                this.sendSecurityLog({type: 'function_constructor_attempt'});
                this.showWarning("Phát hiện hành vi đáng ngờ: Sử dụng Function constructor");
                return () => null;
            };

            // Hook jQuery AJAX nếu có
            if (window.jQuery && window.jQuery.ajax) {
                window.jQuery.ajax = (...args) => {
                    // Cho phép gọi API log của chúng ta
                    if (args[0].url === '/exams-log-session') {
                        return this.originalFunctions.ajax(...args);
                    }
                    return this.originalFunctions.ajax(...args);
                };
            }
        }

        // Override các hàm timing để phát hiện automation
        overrideTimingFunctions() {
            const originalSetTimeout = window.setTimeout;
            const originalSetInterval = window.setInterval;
            const originalRequestAnimationFrame = window.requestAnimationFrame;
            const self = this;

            // Theo dõi các lệnh gọi setTimeout và setInterval
            window.setTimeout = function(callback, delay, ...args) {
                // Phát hiện các mẫu thời gian đáng ngờ
                if (typeof callback === 'function' && delay < 50) {
                    self.sendSecurityLog({
                        type: 'suspicious_timing',
                        method: 'setTimeout',
                        delay: delay
                    });

                    // Kiểm tra nếu callback có chứa click hoặc focus
                    const callbackStr = callback.toString();
                    if (callbackStr.includes('.click(') ||
                        callbackStr.includes('.focus(') ||
                        callbackStr.includes('dispatchEvent') ||
                        callbackStr.includes('MouseEvent')) {
                        self.sendSecurityLog({
                            type: 'auto_click_detected',
                            method: 'setTimeout',
                            code: callbackStr.substring(0, 100)
                        });
                        self.showWarning("Phát hiện công cụ tự động click");
                        return 0; // Không thực thi callback
                    }
                }
                return originalSetTimeout.apply(window, [callback, delay, ...args]);
            };

            window.setInterval = function(callback, delay, ...args) {
                // Phát hiện các mẫu thời gian đáng ngờ
                if (typeof callback === 'function' && delay < 100) {
                    self.sendSecurityLog({
                        type: 'suspicious_timing',
                        method: 'setInterval',
                        delay: delay
                    });

                    // Kiểm tra nếu callback có chứa click hoặc focus
                    const callbackStr = callback.toString();
                    if (callbackStr.includes('.click(') ||
                        callbackStr.includes('.focus(') ||
                        callbackStr.includes('dispatchEvent') ||
                        callbackStr.includes('MouseEvent')) {
                        self.sendSecurityLog({
                            type: 'auto_click_detected',
                            method: 'setInterval',
                            code: callbackStr.substring(0, 100)
                        });
                        self.showWarning("Phát hiện công cụ tự động click");
                        return 0; // Không thực thi callback
                    }
                }
                return originalSetInterval.apply(window, [callback, delay, ...args]);
            };

            // Theo dõi requestAnimationFrame
            window.requestAnimationFrame = function(callback) {
                // Kiểm tra nếu callback có chứa click hoặc focus
                if (typeof callback === 'function') {
                    const callbackStr = callback.toString();
                    if (callbackStr.includes('.click(') ||
                        callbackStr.includes('.focus(') ||
                        callbackStr.includes('dispatchEvent') ||
                        callbackStr.includes('MouseEvent')) {
                        self.sendSecurityLog({
                            type: 'auto_click_detected',
                            method: 'requestAnimationFrame',
                            code: callbackStr.substring(0, 100)
                        });
                        self.showWarning("Phát hiện công cụ tự động click");
                        return 0; // Không thực thi callback
                    }
                }
                return originalRequestAnimationFrame.apply(window, [callback]);
            };
        }

        // Tự sửa chữa
        bindSelfHealing() {
            setInterval(() => {
                // Thêm các kiểm tra khác nếu cần
                this.checkSecurityStatus();
            }, SECURITY_CONFIG.SELF_HEAL_INTERVAL);
        }

        checkSecurityStatus() {
            // Kiểm tra các tính năng bảo mật có bị vô hiệu hóa không
            try {
                // Check DOM protections
                const style = document.createElement('style');
                const computedStyle = window.getComputedStyle(document.body);
                // Allow selection for input fields
                if (computedStyle.userSelect !== 'none' &&
                    !document.activeElement ||
                    (document.activeElement && document.activeElement.tagName !== 'INPUT' &&
                        document.activeElement.tagName !== 'TEXTAREA')) {
                    console.warn("DOM protection compromised");
                    this.sendSecurityLog({type: 'dom_protection_bypass'});
                    this.reapplyDomProtections();
                }

                // Check event listeners
                const events = ['copy', 'cut', 'paste', 'contextmenu'];
                events.forEach(event => {
                    const evt = new Event(event);
                    document.dispatchEvent(evt);
                    if (!evt.defaultPrevented) {
                        console.warn(`${event} protection compromised`);
                        this.sendSecurityLog({type: 'event_protection_bypass', event});
                    }
                });

                this.reviveFunctions();

            } catch (e) {
                console.warn("Security check failed:", e);
                this.sendSecurityLog({type: 'security_check_failed', error: e.message});
            }
        }

        reviveFunctions() {
            if(window.eval.toString() !== "function eval() { [native code] }") {
                window.eval = null;
                console.warn("Eval function tampered");
                this.sendSecurityLog({type: 'function_tampered', function: 'eval'});
            }
        }

        reapplyDomProtections() {
            // Áp dụng lại các bảo vệ DOM nếu bị vô hiệu hóa
            this.injectStyle(`
                *:not(input):not(textarea):not(.fill-blank-input):not(.essay-textarea) {
                    user-select: none !important;
                    -webkit-user-select: none !important;
                    -moz-user-select: none !important;
                    -ms-user-select: none !important;
                }

                input, textarea, .fill-blank-input, .essay-textarea {
                    user-select: text !important;
                    -webkit-user-select: text !important;
                    -moz-user-select: text !important;
                    -ms-user-select: text !important;
                }
            `);
        }

        // Giám sát môi trường
        monitorEnvironment() {
            this.checkDebugger();
            this.detectProxy();
            this.watchMemory();
        }

        checkDebugger() {
            const debugDetector = () => {
                const start = Date.now();
                debugger;
                const diff = Date.now() - start;
                if(diff > SECURITY_CONFIG.DEBUG_DETECTION_DELAY) {
                    console.warn("Debugger detected");
                    this.preventedActions.devTools++;
                    this.sendSecurityLog({type: 'debugger_detected', delay: diff});
                    this.showWarning("Phát hiện công cụ debug");
                }
            };
            setInterval(debugDetector, 10000);
        }

        detectProxy() {
            if(window.Proxy && window.Proxy.toString() !== "function Proxy() { [native code] }") {
                console.warn("Proxy object detected");
                this.sendSecurityLog({type: 'proxy_detected'});
                this.showWarning("Phát hiện Proxy object");
            }
        }

        watchMemory() {
            if(performance.memory && performance.memory.jsHeapSizeLimit > 1e9) {
                console.warn("Abnormal memory usage detected");
                this.sendSecurityLog({type: 'memory_anomaly', size: performance.memory.jsHeapSizeLimit});
            }
        }

        // Chống copy và các thao tác chuột
        initDomProtections() {
            // Chống chuột phải
            document.addEventListener('contextmenu', e => {
                if (e && e.target && (
                    e.target.tagName === 'INPUT' ||
                    e.target.tagName === 'TEXTAREA' ||
                    (e.target.classList && e.target.classList.contains('fill-blank-input')) ||
                    (e.target.classList && e.target.classList.contains('essay-textarea'))
                ))

                e.preventDefault();
                this.preventedActions.rightClick++;
                return false;
            }, true);

            // Chống copy/paste/cut
            ['copy', 'cut', 'paste', 'selectstart', 'select'].forEach(event => {
                document.addEventListener(event, e => {
                    // Allow for input fields and textareas
                    if (e && e.target && (
                        e.target.tagName === 'INPUT' ||
                        e.target.tagName === 'TEXTAREA' ||
                        (e.target.classList && e.target.classList.contains('fill-blank-input')) ||
                        (e.target.classList && e.target.classList.contains('essay-textarea'))
                    )) {
                        return true;
                    }
                    e.preventDefault();

                    if (event === 'copy' || event === 'cut' || event === 'paste') {
                        this.preventedActions[event]++;
                        this.sendSecurityLog({type: 'clipboard_action_prevented', action: event});
                    }

                    return false;
                }, true);
            });

            // Chống kéo chọn text
            document.addEventListener('dragstart', e => {
                // Allow dragging from inputs
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    return true;
                }
                e.preventDefault();
                return false;
            }, true);

            // Chặn phím tắt
            document.addEventListener('keydown', e => {
                // Allow normal keyboard operation in inputs and textareas
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA' ||
                    e.target.classList.contains('fill-blank-input') ||
                    e.target.classList.contains('essay-textarea')) {
                    // Still block key combinations for security
                    if ((e.ctrlKey || e.metaKey) && (
                        e.key === 'c' ||
                        e.key === 'C' ||
                        e.key === 'x' ||
                        e.key === 'X' ||
                        e.key === 'v' ||
                        e.key === 'V' ||
                        e.key === 's' ||
                        e.key === 'S' ||
                        e.key === 'p' ||
                        e.key === 'P'
                    )) {
                        e.preventDefault();
                        this.preventedActions.keyboardShortcuts++;
                        return false;
                    }
                    // Allow typing
                    return true;
                }

                // Block key combinations for non-input elements
                if ((e.ctrlKey || e.metaKey) && (
                    e.key === 'c' ||
                    e.key === 'C' ||
                    e.key === 'x' ||
                    e.key === 'X' ||
                    e.key === 'v' ||
                    e.key === 'V' ||
                    e.key === 'a' ||
                    e.key === 'A' ||
                    e.key === 's' ||
                    e.key === 'S' ||
                    e.key === 'p' ||
                    e.key === 'P'
                )) {
                    e.preventDefault();
                    this.preventedActions.keyboardShortcuts++;
                    return false;
                }
            }, true);

            // CSS chống selection, but allow for inputs and textareas
            this.injectStyle(`
                *:not(input):not(textarea):not(.fill-blank-input):not(.essay-textarea) {
                    user-select: none !important;
                    -webkit-user-select: none !important;
                    -moz-user-select: none !important;
                    -ms-user-select: none !important;
                }

                input, textarea, .fill-blank-input, .essay-textarea {
                    user-select: text !important;
                    -webkit-user-select: text !important;
                    -moz-user-select: text !important;
                    -ms-user-select: text !important;
                }

                @media print {
                    body * {
                        visibility: hidden !important;
                        display: none !important;
                    }

                    body:after {
                        content: "In ấn bị vô hiệu hóa trong quá trình làm bài thi";
                        visibility: visible;
                        display: block;
                        position: absolute;
                        top: 30%;
                        left: 0;
                        right: 0;
                        text-align: center;
                        font-size: 18pt;
                    }
                }
            `);
        }

        injectStyle(css) {
            const style = document.createElement('style');
            style.type = 'text/css';
            style.appendChild(document.createTextNode(css));
            document.head.appendChild(style);
        }

        // Bảo vệ khỏi iframe
        initFrameProtection() {
            if (window.self !== window.top) {
                // Đang trong iframe
                console.warn("Iframe detected");
                this.sendSecurityLog({type: 'iframe_detected'});
                window.top.location = window.self.location;
            }
        }

        // Theo dõi hành vi người dùng
        initBehaviorTracking() {
            // Theo dõi di chuyển chuột - chỉ trên desktop
            if (!this.isMobileDevice) {
                document.addEventListener('mousemove', (e) => {
                    // Lấy mẫu để giảm tải
                    if (Math.random() < 1 / SECURITY_CONFIG.MOUSE_SAMPLE_RATE) {
                        this.mouseMovements.push({
                            x: e.clientX,
                            y: e.clientY,
                            time: Date.now()
                        });

                        // Giới hạn kích thước mảng
                        if (this.mouseMovements.length > 1000) {
                            this.mouseMovements.shift();
                        }
                    }
                });

                // Theo dõi click chuột
                document.addEventListener('click', (e) => {
                    this.mouseClicks.push({
                        x: e.clientX,
                        y: e.clientY,
                        time: Date.now(),
                        target: e.target.tagName
                    });

                    // Giới hạn kích thước mảng
                    if (this.mouseClicks.length > 100) {
                        this.mouseClicks.shift();
                    }

                    // Phân tích mẫu click
                    this.analyzeClickPatterns();
                });
            }

            // Theo dõi nhấn phím - áp dụng cho cả mobile và desktop
            document.addEventListener('keydown', (e) => {
                // Chỉ theo dõi khi người dùng đang nhập vào input hoặc textarea
                if (e.target.tagName === 'INPUT' || e.target.tagName === 'TEXTAREA') {
                    this.keyPressTimings.push({
                        key: e.key,
                        time: Date.now()
                    });

                    // Giới hạn kích thước mảng
                    if (this.keyPressTimings.length > 100) {
                        this.keyPressTimings.shift();
                    }

                    // Phân tích mẫu gõ phím
                    this.analyzeKeyPressPatterns();
                }
            });

            // Theo dõi sự kiện focus/blur - áp dụng cho cả mobile và desktop
            // nhưng với ngưỡng khác nhau
            window.addEventListener('blur', () => {
                this.focusEvents.push({
                    type: 'blur',
                    time: Date.now()
                });

                // Giới hạn kích thước mảng
                if (this.focusEvents.length > 50) {
                    this.focusEvents.shift();
                }
            });

            window.addEventListener('focus', () => {
                this.focusEvents.push({
                    type: 'focus',
                    time: Date.now()
                });

                // Giới hạn kích thước mảng
                if (this.focusEvents.length > 50) {
                    this.focusEvents.shift();
                }

                // Phân tích mẫu focus/blur
                this.analyzeFocusPatterns();
            });

            // Theo dõi sự kiện scroll - áp dụng cho cả mobile và desktop
            document.addEventListener('scroll', () => {
                // Lấy mẫu để giảm tải
                if (Math.random() < 0.1) {
                    const scrollPosition = window.scrollY || document.documentElement.scrollTop;

                    // Lưu vị trí scroll
                    this.scrollEvents = this.scrollEvents || [];
                    this.scrollEvents.push({
                        position: scrollPosition,
                        time: Date.now()
                    });

                    // Giới hạn kích thước mảng
                    if (this.scrollEvents.length > 50) {
                        this.scrollEvents.shift();
                    }
                }
            });

            // Theo dõi sự kiện touch trên thiết bị di động
            if (this.isMobileDevice) {
                document.addEventListener('touchstart', (e) => {
                    this.touchEvents = this.touchEvents || [];
                    this.touchEvents.push({
                        type: 'start',
                        touches: e.touches.length,
                        time: Date.now()
                    });

                    // Giới hạn kích thước mảng
                    if (this.touchEvents.length > 50) {
                        this.touchEvents.shift();
                    }
                });

                document.addEventListener('touchend', (e) => {
                    this.touchEvents = this.touchEvents || [];
                    this.touchEvents.push({
                        type: 'end',
                        time: Date.now()
                    });

                    // Giới hạn kích thước mảng
                    if (this.touchEvents.length > 50) {
                        this.touchEvents.shift();
                    }

                    // Phân tích mẫu touch
                    this.analyzeTouchPatterns();
                });
            }
        }

        analyzeMouseMovement() {
            if (this.mouseMovements.length < 10) return;

            // Tính toán các chỉ số thống kê
            let velocities = this.mouseMovements.map(m => m.velocity).filter(v => !isNaN(v) && isFinite(v));

            if (velocities.length < 5) return;

            // Tính trung bình và độ lệch chuẩn
            const avg = velocities.reduce((a, b) => a + b, 0) / velocities.length;
            const variance = velocities.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / velocities.length;
            const stdDev = Math.sqrt(variance);

            // Tính tỷ lệ chuyển động thẳng
            let straightLineCount = 0;
            for (let i = 2; i < this.mouseMovements.length; i++) {
                const p1 = this.mouseMovements[i-2];
                const p2 = this.mouseMovements[i-1];
                const p3 = this.mouseMovements[i];

                // Kiểm tra 3 điểm có thẳng hàng không
                const area = Math.abs((p1.x*(p2.y-p3.y) + p2.x*(p3.y-p1.y) + p3.x*(p1.y-p2.y))/2);
                if (area < 5) { // Ngưỡng cho chuyển động thẳng
                    straightLineCount++;
                }
            }

            const straightLineRatio = straightLineCount / (this.mouseMovements.length - 2);

            // Lưu kết quả phân tích
            this.mouseAnalysis = {
                avgVelocity: avg,
                stdDevVelocity: stdDev,
                straightLineRatio: straightLineRatio,
                timestamp: Date.now()
            };

            // Phát hiện chuyển động bất thường
            if (stdDev < 0.1 && avg > 0.5) {
                console.warn("Abnormal mouse movement detected: Too consistent");
                this.sendSecurityLog({
                    type: 'abnormal_mouse_movement',
                    avgVelocity: avg,
                    stdDevVelocity: stdDev
                });

                if (straightLineRatio > 0.7) {
                    console.warn("Bot-like mouse movement detected");
                    this.sendSecurityLog({
                        type: 'bot_mouse_movement',
                        straightLineRatio: straightLineRatio
                    });
                    this.showWarning("Phát hiện chuyển động chuột bất thường");
                }
            }
        }

        trackKeyboardInput() {
            let lastKeyTime = 0;

            document.addEventListener('keydown', e => {
                const currentTime = Date.now();
                const timeDiff = currentTime - lastKeyTime;

                // Lưu thời gian giữa các lần nhấn phím
                if (lastKeyTime > 0) {
                    this.keyPressTimings.push(timeDiff);

                    // Giới hạn kích thước mảng
                    if (this.keyPressTimings.length > 50) {
                        this.keyPressTimings.shift();
                    }

                    // Lưu chuỗi phím
                    this.keyPressSequence.push(e.key);
                    if (this.keyPressSequence.length > 20) {
                        this.keyPressSequence.shift();
                    }

                    // Phân tích mẫu gõ phím
                    this.analyzeKeyboardPattern();
                }

                lastKeyTime = currentTime;
            });
        }

        analyzeKeyboardPattern() {
            if (this.keyPressTimings.length < 10) return;

            // Tính trung bình và độ lệch chuẩn
            const avg = this.keyPressTimings.reduce((a, b) => a + b, 0) / this.keyPressTimings.length;
            const variance = this.keyPressTimings.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / this.keyPressTimings.length;
            const stdDev = Math.sqrt(variance);

            // Tính tỷ lệ độ lệch chuẩn trên trung bình
            const variationCoefficient = stdDev / avg;

            // Phát hiện mẫu gõ phím bất thường
            if (variationCoefficient < SECURITY_CONFIG.TYPING_PATTERN_THRESHOLD && avg < SECURITY_CONFIG.KEYBOARD_TIMING_MAX) {
                console.warn("Abnormal keyboard pattern detected");
                this.sendSecurityLog({
                    type: 'abnormal_keyboard_pattern',
                    variationCoefficient: variationCoefficient,
                    avgTiming: avg
                });
                this.showWarning("Phát hiện mẫu gõ phím bất thường");
            }

            // Phát hiện chuỗi phím lặp lại
            this.detectRepeatingKeySequence();
        }

        detectRepeatingKeySequence() {
            if (this.keyPressSequence.length < 10) return;

            const sequence = this.keyPressSequence.join('');

            // Kiểm tra chuỗi lặp lại
            for (let length = 2; length <= 5; length++) {
                for (let i = 0; i < sequence.length - length * 2; i++) {
                    const pattern = sequence.substr(i, length);
                    const nextPattern = sequence.substr(i + length, length);

                    if (pattern === nextPattern) {
                        console.warn("Repeating key sequence detected");
                        this.sendSecurityLog({
                            type: 'repeating_key_sequence',
                            pattern: pattern
                        });
                        this.showWarning("Phát hiện chuỗi phím lặp lại");
                        return;
                    }
                }
            }
        }

        trackClickPatterns() {
            document.addEventListener('click', e => {
                const currentTime = Date.now();

                this.mouseClicks.push({
                    x: e.clientX,
                    y: e.clientY,
                    time: currentTime,
                    target: e.target.tagName,
                    id: e.target.id || '',
                    class: e.target.className || ''
                });

                // Giới hạn kích thước mảng
                if (this.mouseClicks.length > 50) {
                    this.mouseClicks.shift();
                }

                // Phân tích mẫu click
                this.analyzeClickPattern();
            });
        }

        analyzeClickPattern() {
            if (this.mouseClicks.length < 5) return;

            // Tính thời gian giữa các lần click
            const clickIntervals = [];
            for (let i = 1; i < this.mouseClicks.length; i++) {
                clickIntervals.push(this.mouseClicks[i].time - this.mouseClicks[i-1].time);
            }

            // Tính trung bình và độ lệch chuẩn
            const avg = clickIntervals.reduce((a, b) => a + b, 0) / clickIntervals.length;
            const variance = clickIntervals.reduce((a, b) => a + Math.pow(b - avg, 2), 0) / clickIntervals.length;
            const stdDev = Math.sqrt(variance);

            // Phát hiện click quá đều đặn
            if (clickIntervals.length >= SECURITY_CONFIG.CLICK_PATTERN_THRESHOLD && stdDev < 50 && avg < 1000) {
                console.warn("Abnormal click pattern detected");
                this.sendSecurityLog({
                    type: 'abnormal_click_pattern',
                    avgInterval: avg,
                    stdDev: stdDev
                });
                this.showWarning("Phát hiện mẫu click bất thường");
            }

            // Phát hiện click ở cùng một vị trí
            this.detectRepeatedPositionClicks();
        }

        detectRepeatedPositionClicks() {
            if (this.mouseClicks.length < 3) return;

            // Kiểm tra 3 click gần nhất
            const lastThreeClicks = this.mouseClicks.slice(-3);

            // Tính khoảng cách giữa các click
            const dist1 = Math.sqrt(
                Math.pow(lastThreeClicks[0].x - lastThreeClicks[1].x, 2) +
                Math.pow(lastThreeClicks[0].y - lastThreeClicks[1].y, 2)
            );

            const dist2 = Math.sqrt(
                Math.pow(lastThreeClicks[1].x - lastThreeClicks[2].x, 2) +
                Math.pow(lastThreeClicks[1].y - lastThreeClicks[2].y, 2)
            );

            // Nếu khoảng cách quá nhỏ và thời gian quá đều
            if (dist1 < 5 && dist2 < 5) {
                const time1 = lastThreeClicks[1].time - lastThreeClicks[0].time;
                const time2 = lastThreeClicks[2].time - lastThreeClicks[1].time;

                if (Math.abs(time1 - time2) < 100) {
                    console.warn("Repeated position clicks detected");
                    this.sendSecurityLog({
                        type: 'repeated_position_clicks',
                        position: {
                            x: lastThreeClicks[2].x,
                            y: lastThreeClicks[2].y
                        }
                    });
                    this.showWarning("Phát hiện click lặp lại ở cùng vị trí");
                }
            }
        }

        // Gửi log bảo mật
        setupLogReporting() {
            // Set up periodic log sending
            setInterval(() => {
                this.sendLogBatch();
            }, SECURITY_CONFIG.LOG_INTERVAL);

            // Also set up a method to add logs to the queue
            this.addToLogQueue = (logData) => {
                // Add timestamp if not present
                if (!logData.timestamp) {
                    logData.timestamp = Date.now();
                }

                // Add to queue
                this.logQueue.push(logData);

                // If queue is too large, remove oldest logs
                if (this.logQueue.length > SECURITY_CONFIG.LOG_QUEUE_MAX) {
                    // Keep high importance logs, remove low importance ones first
                    this.logQueue.sort((a, b) =>
                        (b.importance || SECURITY_CONFIG.LOG_IMPORTANCE_LEVELS.LOW) -
                        (a.importance || SECURITY_CONFIG.LOG_IMPORTANCE_LEVELS.LOW)
                    );

                    // Trim to max size
                    this.logQueue = this.logQueue.slice(0, SECURITY_CONFIG.LOG_QUEUE_MAX);
                }

                // If it's a high importance log, send immediately
                if (logData.importance >= SECURITY_CONFIG.LOG_IMPORTANCE_LEVELS.HIGH) {
                    this.sendLogBatch();
                }
            };

            // For backward compatibility
            this.sendSecurityLog = (data) => {
                this.addToLogQueue({
                    ...data,
                    importance: SECURITY_CONFIG.LOG_IMPORTANCE_LEVELS.MEDIUM
                });
            };
        }

        sendSecurityLog(data) {
            // Lấy result_id và exam_id từ form
            const resultId = document.getElementById('result_id')?.value;
            const examId = document.getElementById('exam_id')?.value ||
                          document.querySelector('input[name="exam_id"]')?.value;

            if (!resultId || !examId) {
                console.warn("Missing result_id or exam_id for security log");
                return;
            }

            // Chuẩn bị dữ liệu gửi đi
            const logData = {
                result_id: resultId,
                exam_id: examId,
                mouse_data: this.mouseAnalysis || {},
                keyboard_data: {
                    patternCount: this.keyPressTimings.length,
                    timings: this.keyPressTimings.slice(0, 5)
                },
                focus_data: {
                    focusLossCount: this.focusEvents.filter(e => e.type === 'loss').length,
                    totalTime: Date.now() - this.pageLoaded
                },
                device_info: this.deviceFingerprint,
                event_type: 'security_' + (data.type || 'incident'),
                notes: JSON.stringify(data),
                suspicious_level: this.calculateSuspiciousLevel(data)
            };

            // Gửi log qua API
            if (window.jQuery && typeof $.ajax === 'function') {
                $.ajax({
                    url: '/exams-log-session',
                    method: 'POST',
                    data: logData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                        'X-Focus-Loss-Count': logData.focus_data.focusLossCount,
                        'X-Focus-Loss-Time': logData.focus_data.totalTime,
                        'X-Mouse-Data': JSON.stringify(logData.mouse_data || {})
                    },
                    success: function() {
                        // Không làm gì để tránh lộ thông tin
                    },
                    error: function() {
                        // Không làm gì để tránh lộ thông tin
                    }
                });
            } else {
                // Fallback nếu không có jQuery
                const formData = new FormData();
                Object.keys(logData).forEach(key => {
                    if (typeof logData[key] === 'object') {
                        formData.append(key, JSON.stringify(logData[key]));
                    } else {
                        formData.append(key, logData[key]);
                    }
                });

                fetch('/exams-log-session', {
                    method: 'POST',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'),
                        'X-Focus-Loss-Count': logData.focus_data.focusLossCount,
                        'X-Focus-Loss-Time': logData.focus_data.totalTime,
                        'X-Mouse-Data': JSON.stringify(logData.mouse_data || {})
                    },
                    body: formData
                });
            }
        }

        // Tính toán mức độ đáng ngờ dựa trên loại sự kiện
        calculateSuspiciousLevel(data) {
            const suspiciousMap = {
                'periodic_report': 0,
                'dom_modification': 7,
                'copy_attempt': 3,
                'paste_attempt': 4,
                'iframe_detected': 8,
                'abnormal_mouse_movement': 5,
                'bot_mouse_movement': 8,
                'abnormal_keyboard_pattern': 6,
                'repeating_key_sequence': 7,
                'abnormal_click_pattern': 6,
                'repeated_position_clicks': 7,
                'auto_click_detected': 9,
                'auto_focus_detected': 8,
                'synthetic_mouse_event': 9,
                'synthetic_event_dispatch': 9,
                'bot_mouse_pattern': 8,
                'bot_keyboard_pattern': 8,
                'bot_click_pattern': 8,
                'bot_signature_detected': 10,
                'headless_browser_detected': 10,
                'abnormal_latency': 6,
                'exam_terminated': 10
            };

            return suspiciousMap[data.type] || 5; // Mặc định là 5 nếu không tìm thấy
        }

        // Giám sát clipboard
        monitorClipboard() {
            document.addEventListener('copy', () => {
                this.preventedActions.copy++;
                this.sendSecurityLog({type: 'copy_attempt'});
            });

            document.addEventListener('paste', () => {
                this.preventedActions.paste++;
                this.sendSecurityLog({type: 'paste_attempt'});
            });
        }

        // Phát hiện sửa đổi DOM
        detectModification() {
            // Kiểm tra xem các phần tử quan trọng có bị sửa đổi không
            const importantElements = [
                '.question-container',
                '.exam-sidebar',
                '.exam-content',
                '.answer-options'
            ];

            for (const selector of importantElements) {
                const elements = document.querySelectorAll(selector);
                for (const el of elements) {
                    if (el.getAttribute('data-security-hash') === null) {
                        // Tạo hash cho phần tử
                        const hash = this.simpleHash(el.innerHTML);
                        el.setAttribute('data-security-hash', hash);
                    } else {
                        // Kiểm tra hash
                        const oldHash = el.getAttribute('data-security-hash');
                        const newHash = this.simpleHash(el.innerHTML);

                        if (oldHash !== newHash) {
                            console.warn("DOM modification detected");
                            this.sendSecurityLog({
                                type: 'dom_modification',
                                element: selector
                            });

                            // Cập nhật hash mới
                            el.setAttribute('data-security-hash', newHash);
                        }
                    }
                }
            }
        }

        simpleHash(str) {
            let hash = 0;
            if (str.length === 0) return hash;

            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32bit integer
            }

            return hash.toString(16);
        }

        // Hệ thống cảnh báo
        initWarningSystem() {
            // Tạo phần tử cảnh báo
            const warningElement = document.createElement('div');
            warningElement.id = 'security-warning';
            warningElement.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: rgba(255, 0, 0, 0.9);
                color: white;
                padding: 20px;
                border-radius: 10px;
                z-index: 9999;
                text-align: center;
                font-size: 18px;
                box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
                display: none;
            `;
            document.body.appendChild(warningElement);
        }

        showWarning(message) {
            // Nếu đang trong quá trình kết thúc bài thi, không hiển thị cảnh báo nữa
            if (this.warningState.isTerminating) return;

            // Kiểm tra nếu đã đạt tối đa số lần cảnh báo từ localStorage
            const currentWarnings = parseInt(localStorage.getItem(this.STORAGE_KEYS.WARNINGS)) || 0;
            if (currentWarnings >= SECURITY_CONFIG.MAX_WARNINGS) {
                console.log('Max warnings already reached from localStorage, terminating exam');
                this.terminateExam();
                return;
            }

            // Tăng số lần cảnh báo
            this.warningState.warnings = currentWarnings + 1;
            this.warningState.lastWarningTime = Date.now();
            this.warningState.isWarningActive = true;

            // Lưu vào localStorage
            localStorage.setItem(this.STORAGE_KEYS.WARNINGS, this.warningState.warnings.toString());
            localStorage.setItem(this.STORAGE_KEYS.WARNING_ACTIVE, 'true');
            console.log('Protection-v3: Saved warning to localStorage, count:', this.warningState.warnings);

            // Kiểm tra nếu đã đạt đến số lần cảnh báo cho phép
            const isLastWarning = this.warningState.warnings >= SECURITY_CONFIG.MAX_WARNINGS;

            // Tạo nội dung cảnh báo dựa trên loại thiết bị
            let warningContent;

            if (this.isMobileDevice) {
                // Phiên bản mobile - nhỏ gọn hơn
                warningContent = `
                    <div style="font-weight: bold; margin-bottom: 10px; font-size: 18px; color: ${isLastWarning ? '#ff0000' : '#d9534f'}">
                        ${isLastWarning ? 'CẢNH BÁO CUỐI CÙNG' : 'CẢNH BÁO BẢO MẬT'}
                    </div>
                    <div style="font-size: 14px;">${message}</div>
                    <div style="margin-top: 10px;">Cảnh báo ${this.warningState.warnings}/${SECURITY_CONFIG.MAX_WARNINGS}</div>
                    ${isLastWarning ?
                        '<div style="margin-top: 10px; font-size: 13px; color: #ff0000;"><strong>Đây là cảnh báo cuối cùng. Bài thi sẽ kết thúc sau khi đóng thông báo này!</strong></div>' :
                        '<div style="margin-top: 10px; font-size: 13px;">Nếu tiếp tục vi phạm, bài thi sẽ tự động kết thúc.</div>'
                    }
                `;
            } else {
                // Phiên bản desktop - đầy đủ
                warningContent = `
                    <div style="font-weight: bold; margin-bottom: 10px; font-size: 20px; color: ${isLastWarning ? '#ff0000' : '#d9534f'}">
                        ${isLastWarning ? 'CẢNH BÁO CUỐI CÙNG' : 'CẢNH BÁO BẢO MẬT'}
                    </div>
                    <div style="font-size: 16px;">${message}</div>
                    <div style="margin-top: 15px;">Cảnh báo ${this.warningState.warnings}/${SECURITY_CONFIG.MAX_WARNINGS}</div>
                    ${isLastWarning ?
                        '<div style="margin-top: 10px; font-size: 14px; color: #ff0000;"><strong>Đây là cảnh báo cuối cùng. Bài thi sẽ kết thúc sau khi đóng thông báo này!</strong></div>' :
                        '<div style="margin-top: 10px; font-size: 14px;">Nếu tiếp tục vi phạm, bài thi sẽ tự động kết thúc.</div>'
                    }
                `;
            }

            // Hiển thị cảnh báo
            const warningElement = document.getElementById('security-warning') || this.createWarningElement();
            warningElement.innerHTML = warningContent;
            warningElement.style.display = 'block';

            // Tự động ẩn cảnh báo sau một khoảng thời gian
            setTimeout(() => {
                warningElement.style.display = 'none';
                this.warningState.isWarningActive = false;

                // Cập nhật localStorage
                localStorage.setItem(this.STORAGE_KEYS.WARNING_ACTIVE, 'false');

                // Nếu đây là cảnh báo cuối cùng, kết thúc bài thi
                if (isLastWarning) {
                    this.terminateExam();
                }
            }, SECURITY_CONFIG.WARNING_TIMEOUT);
        }

        // Tạo phần tử cảnh báo
        createWarningElement() {
            const newWarningElement = document.createElement('div');
            newWarningElement.id = 'security-warning';

            // Điều chỉnh style dựa trên loại thiết bị
            const mobileStyles = this.isMobileDevice ? `
                max-width: 90%;
                width: 300px;
                padding: 15px;
                font-size: 14px;
            ` : `
                max-width: 80%;
                width: 400px;
                padding: 20px;
                font-size: 16px;
            `;

            newWarningElement.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
                border-radius: 5px;
                z-index: 9999;
                text-align: center;
                box-shadow: 0 0 10px rgba(0,0,0,0.3);
                display: none;
                ${mobileStyles}
            `;
            document.body.appendChild(newWarningElement);
            return newWarningElement;
        }

        terminateExam() {
            // Ngăn chặn việc kết thúc nhiều lần
            if (this.warningState.isTerminating) return;

            console.log("Kết thúc bài thi do vi phạm bảo mật");
            this.warningState.isTerminating = true;

            // Tạo modal thông báo kết thúc
            const terminationModal = document.createElement('div');
            terminationModal.id = 'termination-modal';

            // Điều chỉnh nội dung dựa trên loại thiết bị
            const modalContent = this.isMobileDevice ?
                `<div style="background-color: #fff; padding: 20px; border-radius: 10px; text-align: center; max-width: 90%; width: 300px;">
                    <div style="font-size: 18px; color: #d9534f; font-weight: bold; margin-bottom: 15px;">BÀI THI SẼ KẾT THÚC</div>
                    <div style="font-size: 14px; margin-bottom: 15px;">Bạn đã vi phạm quy định làm bài thi quá số lần cho phép.</div>
                    <div style="font-size: 14px; margin-bottom: 20px;">Bài thi sẽ kết thúc sau <span id="termination-countdown" style="font-weight: bold;">3</span> giây...</div>
                </div>` :
                `<div style="background-color: #fff; padding: 30px; border-radius: 10px; text-align: center; max-width: 500px;">
                    <div style="font-size: 24px; color: #d9534f; font-weight: bold; margin-bottom: 20px;">BÀI THI SẼ KẾT THÚC TỰ ĐỘNG</div>
                    <div style="font-size: 18px; margin-bottom: 20px;">Bạn đã vi phạm quy định làm bài thi quá số lần cho phép.</div>
                    <div style="font-size: 16px; margin-bottom: 30px;">Bài thi sẽ kết thúc sau <span id="termination-countdown" style="font-weight: bold;">3</span> giây...</div>
                </div>`;

            terminationModal.innerHTML = modalContent;

            terminationModal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.8);
                z-index: 10000;
                display: flex;
                justify-content: center;
                align-items: center;
            `;

            document.body.appendChild(terminationModal);

            // Vô hiệu hóa tất cả các input
            this.toggleInputs(false);

            // Gửi log sự kiện kết thúc
            this.sendSecurityLog({
                type: 'exam_terminated',
                reason: 'security_violations',
                warnings: this.warningState.warnings,
                device_type: this.isMobileDevice ? 'mobile' : 'desktop'
            });

            // Đếm ngược trước khi kết thúc
            let countdown = 3;
            const countdownEl = document.getElementById('termination-countdown');

            if (!countdownEl) {
                // Nếu không tìm thấy phần tử đếm ngược, kết thúc ngay lập tức
                this.endExam();
                return;
            }

            const timer = setInterval(() => {
                countdown--;
                countdownEl.textContent = countdown;

                if (countdown <= 0) {
                    clearInterval(timer);
                    // Thực hiện hành động kết thúc bài thi
                    this.endExam();
                }
            }, 1000);
        }

        endExam() {
            // Thực hiện các hành động cần thiết khi kết thúc bài thi
            console.log("Bài thi đã kết thúc do vi phạm bảo mật.");

            // Xóa localStorage khi bài thi kết thúc
            localStorage.removeItem(this.STORAGE_KEYS.WARNINGS);
            localStorage.removeItem(this.STORAGE_KEYS.OUT_OF_FOCUS_COUNT);
            localStorage.removeItem(this.STORAGE_KEYS.WARNING_ACTIVE);
            localStorage.removeItem(this.STORAGE_KEYS.WARNING_END_TIME);
            localStorage.removeItem(this.STORAGE_KEYS.EXAM_SESSION);
            console.log('Protection-v3: Cleared localStorage on exam end');

            // Lấy thông tin cần thiết để redirect
            const resultId = document.getElementById('result_id')?.value;
            const sessionKey = document.querySelector('input[name="session_key"]')?.value ||
                              document.getElementById('session_key')?.value;

            // Gửi log kết thúc bài thi - đảm bảo logs được gửi đi
            try {
                // Gửi logs còn lại trong queue trước
                this.sendLogBatch(true); // Thêm tham số true để đánh dấu đây là lần gửi cuối cùng

                // Sau đó gửi log kết thúc bài thi
                const terminationLog = {
                    type: 'exam_terminated',
                    reason: 'security_violations',
                    warnings: this.warningState.warnings,
                    importance: SECURITY_CONFIG.LOG_IMPORTANCE_LEVELS.HIGH
                };

                // Sử dụng XMLHttpRequest đồng bộ để đảm bảo log được gửi trước khi chuyển trang
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/exams-log-session', false); // false = đồng bộ
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));

                // Chuẩn bị dữ liệu
                const logData = {
                    result_id: resultId,
                    exam_id: document.getElementById('exam_id')?.value,
                    logs: [terminationLog],
                    device_info: this.deviceFingerprint,
                    url: window.location.href,
                    timestamp: Date.now(),
                    is_final: true
                };

                xhr.send(JSON.stringify(logData));
                console.log("Termination log sent:", terminationLog);
            } catch (e) {
                console.error("Failed to send termination log:", e);
            }

            // Nếu có urlEndTest từ biến toàn cục, sử dụng nó
            if (typeof urlEndTest !== 'undefined') {
                window.location.href = urlEndTest;
                return;
            }

            // Các phương thức chuyển hướng khác...
        }

        toggleInputs(disable) {
            const inputs = document.querySelectorAll('input, textarea, select, button');
            inputs.forEach(input => {
                input.disabled = disable;
            });
        }

        // Phát hiện công cụ tự động click
        monitorAutoClickTools() {
            // Theo dõi các sự kiện click tự động
            const originalClick = HTMLElement.prototype.click;
            HTMLElement.prototype.click = function() {
                // Kiểm tra xem click có được gọi từ mã JavaScript không
                const stackTrace = new Error().stack || '';
                const isUserInitiated = stackTrace.includes('at HTMLElement.onclick') ||
                                        stackTrace.includes('at HTMLElement.addEventListener');

                if (!isUserInitiated) {
                    // Click có thể được gọi từ script tự động
                    console.warn("Programmatic click detected");

                    // Kiểm tra xem đây có phải là click hợp lệ từ UI của ứng dụng không
                    const isLegitimateUIClick = stackTrace.includes('exams-testing.js') ||
                                               stackTrace.includes('jquery');

                    if (!isLegitimateUIClick) {
                        console.warn("Suspicious programmatic click detected");

                        // Gửi log và hiển thị cảnh báo
                        if (window._securityEngine) {
                            window._securityEngine.sendSecurityLog({
                                type: 'auto_click_detected',
                                method: 'click_override',
                                stack: stackTrace.substring(0, 200)
                            });

                            window._securityEngine.showWarning("Phát hiện công cụ tự động click");
                        }

                        // Ngăn chặn click
                        return;
                    }
                }

                // Thực hiện click gốc
                return originalClick.apply(this, arguments);
            };

            // Theo dõi các sự kiện focus tự động
            const originalFocus = HTMLElement.prototype.focus;
            HTMLElement.prototype.focus = function() {
                const stackTrace = new Error().stack || '';
                const isUserInitiated = stackTrace.includes('at HTMLElement.onfocus') ||
                                        stackTrace.includes('at HTMLElement.addEventListener');

                if (!isUserInitiated) {
                    // Focus có thể được gọi từ script tự động
                    const isLegitimateUIFocus = stackTrace.includes('exams-testing.js') ||
                                               stackTrace.includes('jquery');

                    if (!isLegitimateUIFocus) {
                        console.warn("Suspicious programmatic focus detected");

                        // Gửi log và hiển thị cảnh báo
                        if (window._securityEngine) {
                            window._securityEngine.sendSecurityLog({
                                type: 'auto_focus_detected',
                                method: 'focus_override',
                                stack: stackTrace.substring(0, 200)
                            });

                            window._securityEngine.showWarning("Phát hiện công cụ tự động focus");
                        }

                        // Ngăn chặn focus
                        return;
                    }
                }

                // Thực hiện focus gốc
                return originalFocus.apply(this, arguments);
            };

            // Theo dõi các sự kiện tạo MouseEvent
            const originalCreateEvent = document.createEvent;
            document.createEvent = function(eventType) {
                if (eventType === 'MouseEvents' || eventType === 'MouseEvent') {
                    const stackTrace = new Error().stack || '';
                    const isLegitimateEvent = stackTrace.includes('exams-testing.js') ||
                                             stackTrace.includes('jquery');

                    if (!isLegitimateEvent) {
                        console.warn("Suspicious MouseEvent creation detected");

                        // Gửi log và hiển thị cảnh báo
                        if (window._securityEngine) {
                            window._securityEngine.sendSecurityLog({
                                type: 'synthetic_mouse_event',
                                stack: stackTrace.substring(0, 200)
                            });

                            window._securityEngine.showWarning("Phát hiện tạo sự kiện chuột giả");
                        }
                    }
                }

                // Thực hiện createEvent gốc
                return originalCreateEvent.apply(this, arguments);
            };

            // Theo dõi dispatchEvent
            const originalDispatchEvent = EventTarget.prototype.dispatchEvent;
            EventTarget.prototype.dispatchEvent = function(event) {
                if (event instanceof MouseEvent || event instanceof KeyboardEvent) {
                    const stackTrace = new Error().stack || '';
                    const isLegitimateDispatch = stackTrace.includes('exams-testing.js') ||
                                                stackTrace.includes('jquery');

                    if (!isLegitimateDispatch) {
                        console.warn("Suspicious event dispatch detected");

                        // Gửi log và hiển thị cảnh báo
                        if (window._securityEngine) {
                            window._securityEngine.sendSecurityLog({
                                type: 'synthetic_event_dispatch',
                                eventType: event.type,
                                stack: stackTrace.substring(0, 200)
                            });

                            window._securityEngine.showWarning("Phát hiện gửi sự kiện giả");
                        }

                        // Ngăn chặn dispatch
                        return false;
                    }
                }

                // Thực hiện dispatchEvent gốc
                return originalDispatchEvent.apply(this, arguments);
            };
        }

        // Phân tích mẫu touch trên thiết bị di động
        analyzeTouchPatterns() {
            if (!this.touchEvents || this.touchEvents.length < 10) return;

            // Kiểm tra các mẫu touch đáng ngờ
            let suspiciousPatterns = 0;

            // Kiểm tra tần suất touch quá đều đặn
            const touchIntervals = [];
            for (let i = 1; i < this.touchEvents.length; i++) {
                if (this.touchEvents[i].type === 'start' && this.touchEvents[i-1].type === 'end') {
                    touchIntervals.push(this.touchEvents[i].time - this.touchEvents[i-1].time);
                }
            }

            if (touchIntervals.length >= 5) {
                // Tính độ lệch chuẩn của khoảng thời gian
                const avg = touchIntervals.reduce((sum, val) => sum + val, 0) / touchIntervals.length;
                const variance = touchIntervals.reduce((sum, val) => sum + Math.pow(val - avg, 2), 0) / touchIntervals.length;
                const stdDev = Math.sqrt(variance);

                // Nếu độ lệch chuẩn quá nhỏ, có thể là bot
                if (stdDev < 50 && avg < 300) {
                    suspiciousPatterns++;
                    console.log("Phát hiện mẫu touch đáng ngờ: khoảng thời gian quá đều đặn");
                }
            }

            // Kiểm tra multi-touch bất thường
            const multiTouchCount = this.touchEvents.filter(e => e.type === 'start' && e.touches > 1).length;
            if (multiTouchCount > 5) {
                suspiciousPatterns++;
                console.log("Phát hiện mẫu touch đáng ngờ: quá nhiều multi-touch");
            }

            // Nếu phát hiện nhiều mẫu đáng ngờ, hiển thị cảnh báo
            if (suspiciousPatterns >= 2) {
                this.showWarning("Phát hiện hành vi touch bất thường. Vui lòng sử dụng thiết bị bình thường để làm bài thi.");
            }
        }

        sendFinalLogs() {
            console.log("Sending final logs before page unload, queue length:", this.logQueue.length);

            // Make sure we have logs to send
            if (this.logQueue.length === 0) {
                return;
            }

            // Lấy result_id và exam_id từ form
            const resultId = document.getElementById('result_id')?.value;
            const examId = document.getElementById('exam_id')?.value ||
                          document.querySelector('input[name="exam_id"]')?.value;

            if (!resultId || !examId) {
                console.warn("Missing result_id or exam_id for final logs");
                return;
            }

            // Prepare all remaining logs
            const logData = {
                result_id: resultId,
                exam_id: examId,
                logs: this.logQueue,
                device_info: this.deviceFingerprint,
                url: window.location.href,
                timestamp: Date.now(),
                is_final: true
            };

            // Use synchronous XMLHttpRequest for beforeunload context
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/exams-log-session', false); // false makes it synchronous

            // Add headers
            xhr.setRequestHeader('Content-Type', 'application/json');
            xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));

            try {
                // Send the request synchronously
                xhr.send(JSON.stringify(logData));
                console.log("Final logs sent, status:", xhr.status);

                // Clear the queue if successful
                if (xhr.status >= 200 && xhr.status < 300) {
                    this.logQueue = [];
                }
            } catch (error) {
                console.error("Failed to send final logs:", error);
            }
        }

        sendLogBatch(isFinal = false) {
            // Check if there are logs to send
            if (this.logQueue.length === 0 || (this.logSendInProgress && !isFinal)) {
                return;
            }

            this.logSendInProgress = true;

            // Prepare batch of logs to send
            const batchSize = Math.min(this.logQueue.length, SECURITY_CONFIG.LOG_BATCH_SIZE);
            const logBatch = this.logQueue.slice(0, batchSize);

            // Lấy result_id và exam_id từ form
            const resultId = document.getElementById('result_id')?.value;
            const examId = document.getElementById('exam_id')?.value ||
                          document.querySelector('input[name="exam_id"]')?.value;

            if (!resultId || !examId) {
                console.warn("Missing result_id or exam_id for security log");
                this.logSendInProgress = false;
                return;
            }

            // Chuẩn bị dữ liệu gửi đi
            const logData = {
                result_id: resultId,
                exam_id: examId,
                logs: logBatch,
                device_info: this.deviceFingerprint,
                url: window.location.href,
                timestamp: Date.now(),
                is_final: isFinal
            };

            // Nếu là lần gửi cuối cùng, sử dụng XMLHttpRequest đồng bộ
            if (isFinal) {
                try {
                    const xhr = new XMLHttpRequest();
                    xhr.open('POST', '/exams-log-session', false); // false = đồng bộ
                    xhr.setRequestHeader('Content-Type', 'application/json');
                    xhr.setRequestHeader('X-CSRF-TOKEN', document.querySelector('meta[name="csrf-token"]')?.getAttribute('content'));
                    xhr.send(JSON.stringify(logData));

                    // Remove sent logs from queue
                    if (xhr.status >= 200 && xhr.status < 300) {
                        this.logQueue.splice(0, batchSize);
                    }
                    this.logSendInProgress = false;
                    this.lastLogSendTime = Date.now();
                    console.log("Final logs sent synchronously:", logBatch);
                } catch (e) {
                    console.error("Failed to send final logs:", e);
                    this.logSendInProgress = false;
                }
                return;
            }

            // Gửi log qua API (bình thường - bất đồng bộ)
            if (window.jQuery && typeof $.ajax === 'function') {
                $.ajax({
                    url: '/exams-log-session',
                    method: 'POST',
                    data: JSON.stringify(logData),
                    contentType: 'application/json',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    },
                    success: () => {
                        // Remove sent logs from queue
                        this.logQueue.splice(0, batchSize);
                        this.logSendInProgress = false;
                        this.lastLogSendTime = Date.now();
                    },
                    error: () => {
                        this.logSendInProgress = false;
                    }
                });
            } else {
                // Fallback nếu không có jQuery
                fetch('/exams-log-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    },
                    body: JSON.stringify(logData)
                })
                .then(() => {
                    // Remove sent logs from queue
                    this.logQueue.splice(0, batchSize);
                    this.logSendInProgress = false;
                    this.lastLogSendTime = Date.now();
                })
                .catch(() => {
                    this.logSendInProgress = false;
                });
            }
        }
    }

    // Khởi tạo bảo mật
    const securityEngine = new SecurityEngine();
})();






















